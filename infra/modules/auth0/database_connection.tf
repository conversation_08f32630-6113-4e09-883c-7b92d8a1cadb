resource "auth0_connection" "qivdb_connection" {
  name     = "${var.env}-QIVDB"
  strategy = "auth0"
  
  options {
    disable_signup                 = true
    requires_username              = true
    brute_force_protection         = true
    enabled_database_customization = true
    import_mode                    = false
    
    password_complexity_options {
      min_length = 1
    }
    
    password_history {
      size   = 5
      enable = false
    }
    
    password_dictionary {
      enable     = false
      dictionary = []
    }
    
    password_no_personal_info {
      enable = false
    }
    
    mfa {
      active                 = true
      return_enroll_settings = true
    }
    
    # These values are encrypted in Auth0 and can't be directly copied
    # You'll need to set these values manually in the Auth0 dashboard
    # or use a secure way to store and retrieve these credentials
    configuration = {
      DB_PASSWORD      = "PLACEHOLDER"
      DB_USER_NAME     = "PLACEHOLDER"
      DB_SERVER_HOST   = "PLACEHOLDER"
      DB_SERVER_PORT   = "PLACEHOLDER"
      EXTERNAL_QIVS_URL = "PLACEHOLDER"
    }
    
    # Custom scripts from the existing QIVSDB connection
    custom_scripts = {
      login          = file("${path.module}/scripts/login.js")
      create         = file("${path.module}/scripts/create.js")
      delete         = file("${path.module}/scripts/delete.js")
      get_user       = file("${path.module}/scripts/get_user.js")
      verify         = file("${path.module}/scripts/verify.js")
      change_password = file("${path.module}/scripts/change_password.js")
    }
  }
  
  # Set the realm for this connection
  realms = ["${var.env}-QIVDB"]
}

resource "auth0_connection_client" "monarch_web_qivdb" {
  connection_id = auth0_connection.qivdb_connection.id
  client_id     = auth0_client.monarch_web.id
}
