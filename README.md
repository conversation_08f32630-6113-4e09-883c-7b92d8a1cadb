# api-user

# Creating a new lambda
- will need to create new parameter store entries for config json object (db connections and other)
- /dev/api-user
- /test/api-user
- /uat/api-user
- /preprod/api-user
- /prod/api-user
- Search and replace api-user with desired lambda name
- will need to create new parameter store entries for integration test config json object (host and apiKey).
- /dev/api-user-test-config
- /uat/api-user-test-config
- /test/api-user-test-config
- /prod/api-user-test-config
- will need to create new parameter store entries for infra.yml.
- /dev/api-user/infra
- /uat/api-user/infra
- /test/api-user/infra
- /prod/api-user/infra


# Run locally
- develop with node 20 (nvm is a handy tool)
- install `serverless`
- create `infra.yml` in project root using `infra.sample.yml` as a reference
- create `config.json` in project root using `config.sample.json` as a reference
- assume cicd role arn:aws:iam::************:role/CICD-RW-Cross-Account
- set the environment variable CODEARTIFACT_AUTH_TOKEN using the following command:
  - for bash: export CODEARTIFACT_AUTH_TOKEN=`` ` ``aws codeartifact get-authorization-token --domain quotable-value --domain-owner ************ --region ap-southeast-2 --query authorizationToken --output text`` ` ``
  - for powershell: $env:CODEARTIFACT_AUTH_TOKEN = (aws codeartifact get-authorization-token --domain quotable-value --domain-owner ************ --region ap-southeast-2 --query authorizationToken --output text)
- `npm install`
- `npm run start`

# Login to ecr for docker db tests
```
aws-mfa --assume-role arn:aws:iam::************:role/CICD-RW-Cross-Account --duration 28800
aws ecr get-login-password | docker login --username AWS --password-stdin  ************.dkr.ecr.ap-southeast-2.amazonaws.com
```

# Run Tests
- See `package.json` for scripts
- create `test/config.json` using `test/config.sample.json` as a reference
- `npm run test` for unit tests
- Powershell: `npm run test:integration:ps` for integration tests
- Bash: `npm run test:integration:bash` for integration tests

## All requests need x-api-key header set
- `npx sls offline` will output the key for local development

## 

### Terraform setup & deployment

We use a pipeline to deploy Terraform changes to environments. You can simply push your changes to your branch, and the pipeline will run automatically.

However, if you need to run Terraform locally, especially to make changes and get fast feedback for deployment,you can follow the steps below:

```shell
cd infra
# this is the role used by the pipeline
aws-mfa --assume-role arn:aws:iam::************:role/ci-cd-account-access-role

# clean up previous runs
rm -rf .terraform
rm .terraform.lock.hcl

terraform init -backend-config="key=api-user/dev"
terraform plan -var-file="config.tfvars"
```
